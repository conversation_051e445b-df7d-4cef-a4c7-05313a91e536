# Enhanced Vector RAG Chat Application

An advanced Retrieval-Augmented Generation (RAG) chat application that uses vector embeddings and ChromaDB to read PDF documents and answer questions with high accuracy.

## Features

- 📄 **PDF Document Loading**: Automatically loads PDF files from the `data` folder
- 🔍 **Text Chunking**: Splits documents into overlapping chunks for better retrieval
- 🧠 **Vector Embeddings**: Uses Sentence Transformers for semantic understanding
- 🗄️ **Vector Database**: ChromaDB for efficient similarity search
- 💬 **Interactive Chat**: Both command-line and web interfaces available
- 🌐 **Web Interface**: Clean, responsive web UI with enhanced features
- 📊 **Similarity Scores**: Shows relevance scores for retrieved information

## Installation

1. **Install Dependencies**:
   ```bash
   pip install -r rag_requirements.txt
   # Or manually:
   pip install PyPDF2 pycryptodome flask sentence-transformers chromadb
   ```

2. **Add PDF Documents**:
   - Place your PDF files in the `data/` folder
   - The application will automatically load all PDF files from this directory

## Usage

### Command Line Interface

Run the command-line chat interface:

```bash
python simple_rag_chat.py
```

- Type your questions and press Enter
- Type 'quit' or 'exit' to stop the application

### Web Interface

1. **Start the Web Server**:
   ```bash
   python web_rag_chat.py
   ```

2. **Open in Browser**:
   - Navigate to `http://localhost:5000`
   - The interface will show the number of loaded documents and text chunks
   - Type your questions in the input field and press Enter or click Send

## How It Works

1. **Document Loading**: The application scans the `data` folder for PDF files and extracts text using PyPDF2

2. **Text Chunking**: Documents are split into overlapping chunks of ~500 words with 50-word overlap to maintain context

3. **Vector Embeddings**: Each text chunk is converted to a high-dimensional vector using Sentence Transformers (all-MiniLM-L6-v2 model)

4. **Vector Database**: Embeddings are stored in ChromaDB for efficient similarity search

5. **Query Processing**: When you ask a question:
   - Your query is converted to a vector embedding
   - ChromaDB performs semantic similarity search to find the most relevant chunks
   - Results are ranked by similarity score

6. **Answer Generation**: The application combines the most relevant chunks with similarity scores to provide enhanced, context-aware answers

## File Structure

```
rag-poc/
├── simple_rag_chat.py      # Command-line chat interface
├── web_rag_chat.py         # Web-based chat interface
├── templates/
│   └── chat.html           # Web interface template
├── data/                   # PDF documents folder
│   └── handbook-investments-1.pdf
├── rag_requirements.txt    # Python dependencies
└── RAG_CHAT_README.md      # This file
```

## Example Questions

Based on the loaded investment handbook, you can ask questions like:

- "What is this document about?"
- "What are the different types of securities mentioned?"
- "How are equity securities measured?"
- "What are the disclosure requirements?"
- "Tell me about foreign currency denominated securities"

## Technical Details

- **PDF Processing**: PyPDF2 with PyCryptodome for encrypted PDFs
- **Embeddings Model**: Sentence Transformers (all-MiniLM-L6-v2) for semantic understanding
- **Vector Database**: ChromaDB for efficient similarity search and storage
- **Similarity Metric**: Cosine similarity in high-dimensional vector space
- **Web Framework**: Flask with simple HTML/CSS/JavaScript frontend
- **No External APIs**: Completely self-contained, no external AI services required

## Limitations

- **Rule-based Answers**: Answers are generated by combining relevant chunks, not by an LLM
- **PDF Only**: Currently supports only PDF documents
- **English Optimized**: Best performance with English text (though model supports multiple languages)
- **Local Processing**: Requires local compute for embeddings (no cloud acceleration)

## Future Enhancements

- Add support for more document formats (Word, TXT, etc.)
- Implement conversation memory/context across queries
- Integrate with LLM APIs (OpenAI, Anthropic) for better answer generation
- Add document upload functionality through the web interface
- Implement query expansion and re-ranking
- Add support for multi-modal documents (images, tables)
- Implement persistent vector database storage

## Troubleshooting

1. **"No documents found"**: Ensure PDF files are in the `data/` folder
2. **"PyCryptodome required"**: Install with `pip install pycryptodome`
3. **Web interface not loading**: Check that Flask is running on port 5000
4. **Poor answer quality**: Try rephrasing your question or adding more specific keywords

---

This is a basic RAG implementation designed for educational purposes and simple document Q&A tasks.
