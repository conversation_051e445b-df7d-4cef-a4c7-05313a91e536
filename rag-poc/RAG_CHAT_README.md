# Simple RAG Chat Application

A simple Retrieval-Augmented Generation (RAG) chat application that reads PDF documents and answers questions based on their content.

## Features

- 📄 **PDF Document Loading**: Automatically loads PDF files from the `data` folder
- 🔍 **Text Chunking**: Splits documents into overlapping chunks for better retrieval
- 🎯 **TF-IDF Search**: Uses TF-IDF similarity to find relevant document sections
- 💬 **Interactive Chat**: Both command-line and web interfaces available
- 🌐 **Web Interface**: Clean, responsive web UI for easy interaction

## Installation

1. **Install Dependencies**:
   ```bash
   pip install PyPDF2 pycryptodome flask
   ```

2. **Add PDF Documents**:
   - Place your PDF files in the `data/` folder
   - The application will automatically load all PDF files from this directory

## Usage

### Command Line Interface

Run the command-line chat interface:

```bash
python simple_rag_chat.py
```

- Type your questions and press Enter
- Type 'quit' or 'exit' to stop the application

### Web Interface

1. **Start the Web Server**:
   ```bash
   python web_rag_chat.py
   ```

2. **Open in Browser**:
   - Navigate to `http://localhost:5000`
   - The interface will show the number of loaded documents and text chunks
   - Type your questions in the input field and press Enter or click Send

## How It Works

1. **Document Loading**: The application scans the `data` folder for PDF files and extracts text using PyPDF2

2. **Text Chunking**: Documents are split into overlapping chunks of ~500 words with 50-word overlap to maintain context

3. **Query Processing**: When you ask a question:
   - Your query is tokenized and processed
   - TF-IDF similarity is calculated between your query and all document chunks
   - The most relevant chunks are retrieved

4. **Answer Generation**: The application combines the most relevant chunks to provide context-aware answers

## File Structure

```
rag-poc/
├── simple_rag_chat.py      # Command-line chat interface
├── web_rag_chat.py         # Web-based chat interface
├── templates/
│   └── chat.html           # Web interface template
├── data/                   # PDF documents folder
│   └── handbook-investments-1.pdf
├── rag_requirements.txt    # Python dependencies
└── RAG_CHAT_README.md      # This file
```

## Example Questions

Based on the loaded investment handbook, you can ask questions like:

- "What is this document about?"
- "What are the different types of securities mentioned?"
- "How are equity securities measured?"
- "What are the disclosure requirements?"
- "Tell me about foreign currency denominated securities"

## Technical Details

- **PDF Processing**: PyPDF2 with PyCryptodome for encrypted PDFs
- **Text Similarity**: TF-IDF (Term Frequency-Inverse Document Frequency)
- **Web Framework**: Flask with simple HTML/CSS/JavaScript frontend
- **No External APIs**: Completely self-contained, no external AI services required

## Limitations

- **Simple Retrieval**: Uses basic TF-IDF similarity (no advanced embeddings)
- **Rule-based Answers**: Answers are generated by combining relevant chunks, not by an LLM
- **PDF Only**: Currently supports only PDF documents
- **English Only**: Optimized for English text processing

## Future Enhancements

- Add support for more document formats (Word, TXT, etc.)
- Implement advanced embedding models for better retrieval
- Add conversation memory/context
- Integrate with LLM APIs for better answer generation
- Add document upload functionality through the web interface

## Troubleshooting

1. **"No documents found"**: Ensure PDF files are in the `data/` folder
2. **"PyCryptodome required"**: Install with `pip install pycryptodome`
3. **Web interface not loading**: Check that Flask is running on port 5000
4. **Poor answer quality**: Try rephrasing your question or adding more specific keywords

---

This is a basic RAG implementation designed for educational purposes and simple document Q&A tasks.
