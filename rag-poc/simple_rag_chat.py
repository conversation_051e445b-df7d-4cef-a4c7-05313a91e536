"""
Simple RAG-based Chat Application
A basic implementation that reads PDFs from the data folder and answers questions
"""

import os
import re
from typing import List, Dict, Tuple
from PyPDF2 import PdfReader
from collections import defaultdict
import math


class SimpleRAGChat:
    def __init__(self, data_folder: str = "data"):
        self.data_folder = data_folder
        self.documents = []
        self.chunks = []
        self.chunk_metadata = []
        self.load_documents()
        
    def load_documents(self):
        """Load all PDF documents from the data folder"""
        print(f"Loading documents from {self.data_folder}...")
        
        if not os.path.exists(self.data_folder):
            print(f"Data folder {self.data_folder} not found!")
            return
            
        pdf_files = [f for f in os.listdir(self.data_folder) if f.endswith('.pdf')]
        
        for pdf_file in pdf_files:
            file_path = os.path.join(self.data_folder, pdf_file)
            try:
                text = self.extract_text_from_pdf(file_path)
                self.documents.append({
                    'filename': pdf_file,
                    'text': text,
                    'path': file_path
                })
                print(f"Loaded: {pdf_file}")
            except Exception as e:
                print(f"Error loading {pdf_file}: {e}")
        
        # Split documents into chunks
        self.create_chunks()
        print(f"Created {len(self.chunks)} text chunks from {len(self.documents)} documents")
    
    def extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from a PDF file"""
        text = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    
    def create_chunks(self, chunk_size: int = 500, overlap: int = 50):
        """Split documents into overlapping chunks"""
        for doc in self.documents:
            text = doc['text']
            words = text.split()
            
            for i in range(0, len(words), chunk_size - overlap):
                chunk_words = words[i:i + chunk_size]
                chunk_text = ' '.join(chunk_words)
                
                self.chunks.append(chunk_text)
                self.chunk_metadata.append({
                    'filename': doc['filename'],
                    'chunk_index': len(self.chunks) - 1,
                    'start_word': i,
                    'end_word': min(i + chunk_size, len(words))
                })
    
    def simple_tokenize(self, text: str) -> List[str]:
        """Simple tokenization - split by words and normalize"""
        # Convert to lowercase and split by non-alphanumeric characters
        words = re.findall(r'\b\w+\b', text.lower())
        return words
    
    def calculate_tf_idf(self, query_tokens: List[str]) -> List[Tuple[int, float]]:
        """Calculate TF-IDF similarity between query and chunks"""
        # Calculate document frequency for each term
        doc_freq = defaultdict(int)
        chunk_tokens = []
        
        for chunk in self.chunks:
            tokens = self.simple_tokenize(chunk)
            chunk_tokens.append(tokens)
            unique_tokens = set(tokens)
            for token in unique_tokens:
                doc_freq[token] += 1
        
        # Calculate TF-IDF scores
        scores = []
        num_docs = len(self.chunks)
        
        for i, tokens in enumerate(chunk_tokens):
            score = 0
            token_freq = defaultdict(int)
            
            # Calculate term frequency in this chunk
            for token in tokens:
                token_freq[token] += 1
            
            # Calculate TF-IDF score for query terms
            for query_token in query_tokens:
                if query_token in token_freq:
                    tf = token_freq[query_token] / len(tokens)
                    idf = math.log(num_docs / (doc_freq[query_token] + 1))
                    score += tf * idf
            
            scores.append((i, score))
        
        # Sort by score descending
        scores.sort(key=lambda x: x[1], reverse=True)
        return scores
    
    def retrieve_relevant_chunks(self, query: str, top_k: int = 3) -> List[Dict]:
        """Retrieve the most relevant chunks for a query"""
        query_tokens = self.simple_tokenize(query)
        scores = self.calculate_tf_idf(query_tokens)
        
        relevant_chunks = []
        for i, (chunk_idx, score) in enumerate(scores[:top_k]):
            if score > 0:  # Only include chunks with positive similarity
                relevant_chunks.append({
                    'text': self.chunks[chunk_idx],
                    'metadata': self.chunk_metadata[chunk_idx],
                    'score': score,
                    'rank': i + 1
                })
        
        return relevant_chunks
    
    def generate_answer(self, query: str, relevant_chunks: List[Dict]) -> str:
        """Generate an answer based on relevant chunks (simple rule-based approach)"""
        if not relevant_chunks:
            return "I couldn't find relevant information in the documents to answer your question."
        
        # Combine the most relevant chunks
        context = "\n\n".join([chunk['text'] for chunk in relevant_chunks[:2]])
        
        # Simple answer generation based on context
        answer = f"Based on the document(s), here's what I found:\n\n"
        
        # Add context from the most relevant chunk
        best_chunk = relevant_chunks[0]
        answer += f"From {best_chunk['metadata']['filename']}:\n"
        answer += f"{best_chunk['text'][:500]}..."
        
        if len(relevant_chunks) > 1:
            answer += f"\n\nAdditional relevant information from the same document:\n"
            answer += f"{relevant_chunks[1]['text'][:300]}..."
        
        return answer
    
    def chat(self, query: str) -> Dict:
        """Main chat function"""
        print(f"\nUser: {query}")
        
        # Retrieve relevant chunks
        relevant_chunks = self.retrieve_relevant_chunks(query, top_k=3)
        
        # Generate answer
        answer = self.generate_answer(query, relevant_chunks)
        
        print(f"Assistant: {answer}")
        
        return {
            'query': query,
            'answer': answer,
            'relevant_chunks': len(relevant_chunks),
            'sources': [chunk['metadata']['filename'] for chunk in relevant_chunks]
        }


def main():
    """Main function to run the chat application"""
    print("=== Simple RAG Chat Application ===")
    print("Ask questions about the documents in the data folder.")
    print("Type 'quit' or 'exit' to stop.\n")
    
    # Initialize the RAG system
    rag_chat = SimpleRAGChat()
    
    if not rag_chat.documents:
        print("No documents found. Please add PDF files to the backend/data folder.")
        return
    
    # Chat loop
    while True:
        try:
            user_input = input("\nYour question: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            if not user_input:
                continue
            
            # Process the query
            result = rag_chat.chat(user_input)
            
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    main()
