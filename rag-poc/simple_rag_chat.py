"""
Enhanced RAG-based Chat Application
An advanced implementation using vector embeddings and ChromaDB for better retrieval
"""

import os
from typing import List, Dict
from PyPDF2 import PdfReader

# Vector database and embeddings
import chromadb
from sentence_transformers import SentenceTransformer


class VectorRAGChat:
    def __init__(self, data_folder: str = "data", model_name: str = "all-MiniLM-L6-v2"):
        self.data_folder = data_folder
        self.documents = []
        self.chunks = []
        self.chunk_metadata = []

        # Initialize embedding model
        print("Loading embedding model...")
        self.embedding_model = SentenceTransformer(model_name)

        # Initialize ChromaDB
        print("Initializing vector database...")
        self.chroma_client = chromadb.Client()

        # Create or get collection
        try:
            self.collection = self.chroma_client.get_collection("rag_documents")
            print("Found existing vector database collection")
        except:
            self.collection = self.chroma_client.create_collection("rag_documents")
            print("Created new vector database collection")

        self.load_documents()
        
    def load_documents(self):
        """Load all PDF documents from the data folder and create embeddings"""
        print(f"Loading documents from {self.data_folder}...")

        if not os.path.exists(self.data_folder):
            print(f"Data folder {self.data_folder} not found!")
            return

        pdf_files = [f for f in os.listdir(self.data_folder) if f.endswith('.pdf')]

        # Check if documents are already in the vector database
        existing_count = self.collection.count()
        if existing_count > 0:
            print(f"Found {existing_count} existing chunks in vector database")
            return

        for pdf_file in pdf_files:
            file_path = os.path.join(self.data_folder, pdf_file)
            try:
                text = self.extract_text_from_pdf(file_path)
                self.documents.append({
                    'filename': pdf_file,
                    'text': text,
                    'path': file_path
                })
                print(f"Loaded: {pdf_file}")
            except Exception as e:
                print(f"Error loading {pdf_file}: {e}")

        if self.documents:
            # Split documents into chunks and create embeddings
            self.create_chunks_and_embeddings()
            print(f"Created {len(self.chunks)} text chunks with embeddings")
    
    def extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from a PDF file"""
        text = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    
    def create_chunks_and_embeddings(self, chunk_size: int = 500, overlap: int = 50):
        """Split documents into overlapping chunks and create embeddings"""
        print("Creating text chunks and embeddings...")

        all_chunks = []
        all_metadata = []
        all_ids = []

        for doc in self.documents:
            text = doc['text']
            words = text.split()

            for i in range(0, len(words), chunk_size - overlap):
                chunk_words = words[i:i + chunk_size]
                chunk_text = ' '.join(chunk_words)

                if len(chunk_text.strip()) < 50:  # Skip very short chunks
                    continue

                chunk_id = f"{doc['filename']}_{i}"
                metadata = {
                    'filename': doc['filename'],
                    'start_word': i,
                    'end_word': min(i + chunk_size, len(words)),
                    'chunk_length': len(chunk_text)
                }

                all_chunks.append(chunk_text)
                all_metadata.append(metadata)
                all_ids.append(chunk_id)

                self.chunks.append(chunk_text)
                self.chunk_metadata.append(metadata)

        if all_chunks:
            print(f"Creating embeddings for {len(all_chunks)} chunks...")
            # Create embeddings in batches for efficiency
            batch_size = 32
            for i in range(0, len(all_chunks), batch_size):
                batch_chunks = all_chunks[i:i + batch_size]
                batch_metadata = all_metadata[i:i + batch_size]
                batch_ids = all_ids[i:i + batch_size]

                # Create embeddings
                embeddings = self.embedding_model.encode(batch_chunks).tolist()

                # Add to ChromaDB
                self.collection.add(
                    documents=batch_chunks,
                    metadatas=batch_metadata,
                    embeddings=embeddings,
                    ids=batch_ids
                )

                print(f"Processed {min(i + batch_size, len(all_chunks))}/{len(all_chunks)} chunks")
    
    def retrieve_relevant_chunks(self, query: str, top_k: int = 3) -> List[Dict]:
        """Retrieve the most relevant chunks using vector similarity"""
        print(f"Searching for: {query}")

        # Create query embedding
        query_embedding = self.embedding_model.encode([query]).tolist()[0]

        # Search in ChromaDB
        results = self.collection.query(
            query_embeddings=[query_embedding],
            n_results=top_k
        )

        relevant_chunks = []
        if results['documents'] and results['documents'][0]:
            for i, (doc, metadata, distance) in enumerate(zip(
                results['documents'][0],
                results['metadatas'][0],
                results['distances'][0]
            )):
                # Convert distance to similarity score (lower distance = higher similarity)
                similarity_score = 1.0 / (1.0 + distance)

                relevant_chunks.append({
                    'text': doc,
                    'metadata': metadata,
                    'score': similarity_score,
                    'distance': distance,
                    'rank': i + 1
                })

        return relevant_chunks
    
    def generate_answer(self, query: str, relevant_chunks: List[Dict]) -> str:
        """Generate an enhanced answer based on relevant chunks"""
        if not relevant_chunks:
            return "I couldn't find relevant information in the documents to answer your question."

        # Build a comprehensive answer
        answer_parts = []
        answer_parts.append("Based on the documents, here's what I found:\n")

        # Add information from each relevant chunk
        for i, chunk in enumerate(relevant_chunks):
            similarity_score = chunk['score']
            filename = chunk['metadata']['filename']

            answer_parts.append(f"\n**Source {i+1}** (from {filename}, similarity: {similarity_score:.3f}):")

            # Clean up the text and limit length
            text = chunk['text'].strip()
            if len(text) > 400:
                text = text[:400] + "..."

            answer_parts.append(text)

        # Add summary if multiple chunks
        if len(relevant_chunks) > 1:
            answer_parts.append(f"\n**Summary**: Found {len(relevant_chunks)} relevant sections from the documents that address your question about: {query}")

        return "\n".join(answer_parts)
    
    def chat(self, query: str) -> Dict:
        """Enhanced chat function with vector similarity"""
        print(f"\nUser: {query}")

        # Retrieve relevant chunks using vector similarity
        relevant_chunks = self.retrieve_relevant_chunks(query, top_k=5)

        # Generate enhanced answer
        answer = self.generate_answer(query, relevant_chunks)

        print(f"Assistant: {answer}")

        return {
            'query': query,
            'answer': answer,
            'relevant_chunks': len(relevant_chunks),
            'sources': list(set([chunk['metadata']['filename'] for chunk in relevant_chunks])),
            'similarity_scores': [chunk['score'] for chunk in relevant_chunks]
        }

    def get_collection_stats(self) -> Dict:
        """Get statistics about the vector database collection"""
        count = self.collection.count()
        return {
            'total_chunks': count,
            'documents_loaded': len(self.documents),
            'collection_name': self.collection.name
        }


def main():
    """Main function to run the enhanced RAG chat application"""
    print("=== Enhanced Vector RAG Chat Application ===")
    print("Ask questions about the documents in the data folder.")
    print("Type 'quit', 'exit', or 'stats' for collection statistics.\n")

    try:
        # Initialize the enhanced RAG system
        print("Initializing enhanced RAG system...")
        rag_chat = VectorRAGChat()

        # Show collection stats
        stats = rag_chat.get_collection_stats()
        print(f"✓ Ready! Vector database contains {stats['total_chunks']} chunks from {stats['documents_loaded']} documents\n")

        # Chat loop
        while True:
            try:
                user_input = input("Your question: ").strip()

                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("Goodbye!")
                    break

                if user_input.lower() == 'stats':
                    stats = rag_chat.get_collection_stats()
                    print(f"Collection Stats: {stats}")
                    continue

                if not user_input:
                    continue

                # Process the query
                result = rag_chat.chat(user_input)
                print(f"Found {result['relevant_chunks']} relevant chunks from: {', '.join(result['sources'])}")

            except KeyboardInterrupt:
                print("\nGoodbye!")
                break
            except Exception as e:
                print(f"Error processing query: {e}")

    except Exception as e:
        print(f"Error initializing RAG system: {e}")
        print("Make sure you have the required dependencies installed:")
        print("pip install sentence-transformers chromadb")


if __name__ == "__main__":
    main()
