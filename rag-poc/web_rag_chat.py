"""
Web-based RAG Chat Application
A simple Flask web interface for the RAG chat system
"""

from flask import Flask, render_template, request, jsonify
from simple_rag_chat import Vector<PERSON>GChat

app = Flask(__name__)

# Initialize the enhanced vector RAG chat system
print("Initializing Vector RAG Chat system for web interface...")
rag_chat = VectorRAGChat()

@app.route('/')
def index():
    """Main chat interface"""
    return render_template('chat.html')

@app.route('/api/chat', methods=['POST'])
def chat_api():
    """API endpoint for chat"""
    try:
        data = request.get_json()
        query = data.get('query', '').strip()
        
        if not query:
            return jsonify({'error': 'Query is required'}), 400
        
        # Process the query
        result = rag_chat.chat(query)
        
        return jsonify({
            'success': True,
            'answer': result['answer'],
            'sources': result['sources'],
            'relevant_chunks': result['relevant_chunks']
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status')
def status():
    """Get enhanced system status with vector database info"""
    stats = rag_chat.get_collection_stats()
    return jsonify({
        'documents_loaded': stats['documents_loaded'],
        'chunks_created': stats['total_chunks'],
        'collection_name': stats['collection_name'],
        'document_names': [doc['filename'] for doc in rag_chat.documents],
        'system_type': 'Vector RAG with ChromaDB + Sentence Transformers'
    })

if __name__ == '__main__':
    print("Starting Enhanced Vector RAG Chat Web Application...")
    stats = rag_chat.get_collection_stats()
    print(f"✓ Loaded {stats['documents_loaded']} documents")
    print(f"✓ Vector database contains {stats['total_chunks']} text chunks")
    print(f"✓ Using {stats['collection_name']} collection")
    print("\n🌐 Access the enhanced chat interface at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
