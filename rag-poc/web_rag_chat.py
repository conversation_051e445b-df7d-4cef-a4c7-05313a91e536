"""
Web-based RAG Chat Application
A simple Flask web interface for the RAG chat system
"""

from flask import Flask, render_template, request, jsonify
from simple_rag_chat import SimpleRA<PERSON>hat

app = Flask(__name__)

# Initialize the RAG chat system
rag_chat = SimpleRAGChat()

@app.route('/')
def index():
    """Main chat interface"""
    return render_template('chat.html')

@app.route('/api/chat', methods=['POST'])
def chat_api():
    """API endpoint for chat"""
    try:
        data = request.get_json()
        query = data.get('query', '').strip()
        
        if not query:
            return jsonify({'error': 'Query is required'}), 400
        
        # Process the query
        result = rag_chat.chat(query)
        
        return jsonify({
            'success': True,
            'answer': result['answer'],
            'sources': result['sources'],
            'relevant_chunks': result['relevant_chunks']
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/status')
def status():
    """Get system status"""
    return jsonify({
        'documents_loaded': len(rag_chat.documents),
        'chunks_created': len(rag_chat.chunks),
        'document_names': [doc['filename'] for doc in rag_chat.documents]
    })

if __name__ == '__main__':
    print("Starting RAG Chat Web Application...")
    print(f"Loaded {len(rag_chat.documents)} documents")
    print(f"Created {len(rag_chat.chunks)} text chunks")
    print("\nAccess the chat interface at: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
