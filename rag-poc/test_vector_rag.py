#!/usr/bin/env python3
"""
Test script for the Enhanced Vector RAG Chat Application
"""

from simple_rag_chat import VectorRAGChat
import time

def test_vector_rag():
    """Test the vector RAG functionality"""
    print("=== Testing Enhanced Vector RAG Chat ===\n")
    
    # Initialize the system
    print("1. Initializing Vector RAG system...")
    start_time = time.time()
    rag_chat = VectorRAGChat()
    init_time = time.time() - start_time
    print(f"   ✓ Initialization completed in {init_time:.2f} seconds\n")
    
    # Get system statistics
    print("2. System Statistics:")
    stats = rag_chat.get_collection_stats()
    print(f"   • Documents loaded: {stats['documents_loaded']}")
    print(f"   • Total chunks: {stats['total_chunks']}")
    print(f"   • Collection name: {stats['collection_name']}\n")
    
    # Test queries
    test_queries = [
        "What are the different types of securities?",
        "How are equity securities measured?",
        "What is the difference between HTM and AFS?",
        "Tell me about foreign currency securities",
        "What are the disclosure requirements?"
    ]
    
    print("3. Testing Queries:")
    for i, query in enumerate(test_queries, 1):
        print(f"\n   Query {i}: {query}")
        start_time = time.time()
        
        result = rag_chat.chat(query)
        query_time = time.time() - start_time
        
        print(f"   ✓ Response time: {query_time:.2f} seconds")
        print(f"   ✓ Found {result['relevant_chunks']} relevant chunks")
        print(f"   ✓ Sources: {', '.join(result['sources'])}")
        if result['similarity_scores']:
            avg_score = sum(result['similarity_scores']) / len(result['similarity_scores'])
            print(f"   ✓ Average similarity score: {avg_score:.3f}")
        print(f"   ✓ Answer length: {len(result['answer'])} characters")
    
    print("\n=== Test Completed Successfully! ===")

if __name__ == "__main__":
    try:
        test_vector_rag()
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
