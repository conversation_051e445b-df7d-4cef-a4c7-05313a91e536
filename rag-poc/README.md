# CMDB Management System

A comprehensive Configuration Management Database (CMDB) solution with AI-powered analysis — built for infrastructure management, obsolescence detection, and intelligent insights using FastAPI, React, and LLM integration via Ollama.

---

## 🔧 Prerequisites

- Node.js (v18+)
- Python 3.9+
- [O<PERSON>ma](https://ollama.com) installed and configured (for AI features)

---

## 🚀 Getting Started

### 2. Start the Backend (FastAPI)

#### Install Python dependencies

```bash
cd backend
pip install -r requirements.txt
```

#### Run FastAPI server

```bash
uvicorn main:app --reload --port 8003
```

---

### 3. Start the Frontend (React)

```bash
cd ../frontend
npm install
npm run dev
```

App will be running on: [http://localhost:5173](http://localhost:5173)

---

### 4. Start Ollama with `llava` (Optional - for AI features)

```bash
ollama pull llava
ollama serve
```

Ensure it's running on default port `11434`. The CMDB system will work without <PERSON>llama, but AI features like obsolescence analysis and chat will be disabled.

---

## 📂 Project Structure

```
cmdb-management/
│
├── frontend/                    # React frontend
│   ├── src/components/         # CMDB components
│   ├── src/pages/             # Main pages
│   └── package.json
├── backend/                    # FastAPI backend
│   ├── main.py                # CMDB API endpoints
│   ├── cmdb_reader.py         # Data processing
│   ├── cmdb_obsolescence_agent.py  # AI analysis
│   ├── cmdb_chat_agent.py     # Chat interface
│   ├── cmdb_data/             # Excel data files
│   └── requirements.txt
└── README.md
```

---

## 🧠 AI Features

- Using `llava` model from Ollama for infrastructure analysis and chat
- AI-powered obsolescence detection for identifying outdated technology
- Natural language interface for querying CMDB data

---

## 🛠 API Endpoints

```
GET  /cmdb/data              # Get all CMDB data
GET  /cmdb/servers           # Get database servers
GET  /cmdb/apps              # Get application mappings
GET  /cmdb/search?q=query    # Search CMDB data
GET  /cmdb/obsolete          # Get obsolescence analysis
POST /cmdb/chat              # Chat with AI assistant
GET  /cmdb/stats             # Get summary statistics
```

---

## 🧪 Example API Tests

```bash
# Get all CMDB data
curl http://localhost:8003/cmdb/data

# Search for PostgreSQL servers
curl "http://localhost:8003/cmdb/search?q=PostgreSQL"

# Get obsolescence analysis
curl "http://localhost:8003/cmdb/obsolete?risk_threshold=70"

# Chat with AI
curl -X POST http://localhost:8003/cmdb/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "How many servers do we have?"}'
```

## 🎯 Features

- **Infrastructure Inventory**: Complete database server and application catalog
- **AI-Powered Analysis**: Obsolescence detection and risk assessment
- **Interactive Chat**: Natural language queries about your infrastructure
- **Search & Filter**: Find specific servers, applications, or configurations
- **Business Intelligence**: Insights across business units and technology stacks
- **Modern UI**: Responsive React interface with dark mode support

---

## Simple workflow for KYC check -

- KYC Verification Flow (LangGraph + Ollama)
- Input Upload (React UI)
  → User uploads an image or PDF document.

- Document Extraction (Python FastAPI + Ollama)
  → FastAPI receives the file, sends it to llava via Ollama, which extracts raw text.

## LangGraph Workflow Starts

✅ Node 1 — Extract Node

- Input: Extracted text from Ollama.

- Task: Preprocess, clean, and structure the extracted text into KYC fields (name, DOB, document number).

✅ Node 2 — Validation Node

- Input: Structured KYC data.

- Task: Apply regex, logic, or external APIs to validate the correctness of fields.
  (e.g., check if PAN number is valid format, DOB is logical.)

✅ Node 3 — Enrichment Node

- Input: Validated KYC data.

- Task: Enrich with external APIs (e.g., sanctions list, AML screening, address lookup).

✅ Node 4 — Decision Node

- Input: Enriched KYC data.

- Task: Apply rules (if/else) or AI to decide APPROVED, REJECTED, or REVIEW.

User Uploads Image
│
▼
[extract_text() → Ollama LLaVA]  
 │
▼
LangGraph Flow:
+-------------------------------+
| extract_kyc |
| ↳ Regex parse Name, DOB, |
| Address, Document Number |
+-------------------------------+
│
▼
+-------------------------------+
| address_cleaner |
| ↳ Clean Address format |
+-------------------------------+
│
▼
+-------------------------------+
| dob_validator |
| ↳ Validate Date of Birth |
| (Check if user is over 18) |
+-------------------------------+
│
▼
+-------------------------------+
| validate_kyc |
| ↳ Check if all fields are |
| valid (Full Name, DOB, |
| Address, Document Number, |
| Age verified) |
+-------------------------------+
│
(valid) ──✔️──> Return KYC Data
(invalid) ──❌──> Return Error

## 📝 License

MIT License
