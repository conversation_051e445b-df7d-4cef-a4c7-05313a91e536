<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG Chat - Document Q&A</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status {
            background-color: #34495e;
            color: white;
            padding: 0.5rem;
            text-align: center;
            font-size: 0.9rem;
        }
        
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
            padding: 1rem;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: white;
            border-radius: 8px;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            min-height: 400px;
        }
        
        .message {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border-radius: 8px;
            max-width: 80%;
        }
        
        .user-message {
            background-color: #3498db;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        
        .bot-message {
            background-color: #ecf0f1;
            color: #2c3e50;
            border-left: 4px solid #3498db;
        }
        
        .sources {
            font-size: 0.8rem;
            color: #7f8c8d;
            margin-top: 0.5rem;
            font-style: italic;
        }
        
        .input-container {
            display: flex;
            gap: 0.5rem;
            background: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        #queryInput {
            flex: 1;
            padding: 0.75rem;
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            font-size: 1rem;
            outline: none;
        }
        
        #queryInput:focus {
            border-color: #3498db;
        }
        
        #sendButton {
            padding: 0.75rem 1.5rem;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s;
        }
        
        #sendButton:hover {
            background-color: #2980b9;
        }
        
        #sendButton:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            padding: 1rem;
        }
        
        .welcome-message {
            text-align: center;
            color: #7f8c8d;
            padding: 2rem;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📚 RAG Chat - Document Q&A</h1>
        <p>Ask questions about your documents</p>
    </div>
    
    <div class="status" id="status">
        Loading system status...
    </div>
    
    <div class="chat-container">
        <div class="chat-messages" id="chatMessages">
            <div class="welcome-message">
                Welcome! Ask me anything about the loaded documents. I'll search through the content and provide relevant answers.
            </div>
        </div>
        
        <div class="loading" id="loading">
            🤔 Thinking...
        </div>
        
        <div class="input-container">
            <input type="text" id="queryInput" placeholder="Ask a question about the documents..." 
                   onkeypress="handleKeyPress(event)">
            <button id="sendButton" onclick="sendMessage()">Send</button>
        </div>
    </div>

    <script>
        // Load system status
        fetch('/api/status')
            .then(response => response.json())
            .then(data => {
                document.getElementById('status').innerHTML = 
                    `📄 ${data.documents_loaded} documents loaded | 📝 ${data.chunks_created} text chunks | 📁 Files: ${data.document_names.join(', ')}`;
            })
            .catch(error => {
                document.getElementById('status').innerHTML = '❌ Error loading system status';
            });

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function sendMessage() {
            const queryInput = document.getElementById('queryInput');
            const query = queryInput.value.trim();
            
            if (!query) return;
            
            // Add user message to chat
            addMessage(query, 'user');
            
            // Clear input and disable button
            queryInput.value = '';
            document.getElementById('sendButton').disabled = true;
            document.getElementById('loading').style.display = 'block';
            
            // Send query to API
            fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query: query })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addMessage(data.answer, 'bot', data.sources);
                } else {
                    addMessage('Sorry, there was an error processing your question: ' + data.error, 'bot');
                }
            })
            .catch(error => {
                addMessage('Sorry, there was an error connecting to the server.', 'bot');
            })
            .finally(() => {
                document.getElementById('sendButton').disabled = false;
                document.getElementById('loading').style.display = 'none';
                queryInput.focus();
            });
        }

        function addMessage(text, sender, sources = null) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            let messageContent = text;
            if (sources && sources.length > 0) {
                messageContent += `<div class="sources">📎 Sources: ${sources.join(', ')}</div>`;
            }
            
            messageDiv.innerHTML = messageContent;
            chatMessages.appendChild(messageDiv);
            
            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Focus on input when page loads
        document.getElementById('queryInput').focus();
    </script>
</body>
</html>
