"""
CMDB Chat Agent
LLM-powered chat interface for querying CMDB data
"""
import requests
import json
import logging
from typing import Dict, List, Any, Optional
from cmdb_reader import cmdb_reader

logger = logging.getLogger(__name__)

class CMDBChatAgent:
    """Chat agent for answering questions about CMDB data"""
    
    def __init__(self, model_url: str = "http://localhost:11434/api/generate", model_name: str = "llava"):
        self.model_url = model_url
        self.model_name = model_name
        self.conversation_history = []
        
    def _call_llm(self, prompt: str) -> str:
        """Call the LLM with a prompt and return the response"""
        try:
            response = requests.post(
                self.model_url,
                json={
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": False
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "").strip()
            else:
                logger.error(f"LLM API error: {response.status_code}")
                return "I'm sorry, I'm having trouble accessing the AI model right now."
                
        except Exception as e:
            logger.error(f"Error calling LLM: {e}")
            return "I'm sorry, I encountered an error while processing your request."
    
    def _get_relevant_data_context(self, user_question: str) -> str:
        """Get relevant CMDB data based on the user's question"""
        question_lower = user_question.lower()
        
        # Get all data
        db_servers = cmdb_reader.get_database_servers()
        app_mappings = cmdb_reader.get_app_mappings()
        
        context_parts = []
        
        # Add summary statistics
        stats = cmdb_reader.get_summary_stats()
        context_parts.append(f"CMDB Summary Statistics:\n{json.dumps(stats, indent=2)}")
        
        # If question mentions specific terms, include relevant data
        if any(term in question_lower for term in ['server', 'database', 'host', 'cpu', 'ram', 'memory']):
            # Include sample database servers and unique values
            context_parts.append(f"\nDatabase Server Sample (first 5 records):\n{json.dumps(db_servers[:5], indent=2)}")
            context_parts.append(f"\nAvailable Database Types: {cmdb_reader.get_unique_values('database_servers', 'Database')}")
            context_parts.append(f"Available Operating Systems: {cmdb_reader.get_unique_values('database_servers', 'Operating System')}")
        
        if any(term in question_lower for term in ['app', 'application', 'business', 'owner', 'team']):
            # Include sample app mappings
            context_parts.append(f"\nApplication Mapping Sample (first 5 records):\n{json.dumps(app_mappings[:5], indent=2)}")
            context_parts.append(f"\nBusiness Units: {cmdb_reader.get_unique_values('app_mappings', 'Business Unit')}")
        
        # If asking about specific values, search for them
        search_terms = []
        for word in user_question.split():
            if len(word) > 3:  # Only search for meaningful words
                search_results = cmdb_reader.search_servers(word)
                if search_results:
                    context_parts.append(f"\nSearch results for '{word}':\n{json.dumps(search_results[:3], indent=2)}")
        
        return "\n".join(context_parts)
    
    def chat(self, user_message: str, session_id: Optional[str] = None) -> Dict[str, Any]:
        """Process a chat message and return a response"""
        
        # Get relevant CMDB data context
        data_context = self._get_relevant_data_context(user_message)
        
        # Build the prompt with context
        prompt = f"""
        You are a helpful CMDB (Configuration Management Database) assistant. You have access to information about database servers and application mappings.
        
        Current CMDB Data Context:
        {data_context}
        
        User Question: {user_message}
        
        Please provide a helpful and accurate answer based on the CMDB data provided. If you need to reference specific servers or applications, use the data from the context above. If the question cannot be answered with the available data, explain what information is missing.
        
        Keep your response conversational and helpful. If providing lists or statistics, format them clearly.
        """
        
        # Get LLM response
        llm_response = self._call_llm(prompt)
        
        # Store conversation history
        conversation_entry = {
            "user_message": user_message,
            "assistant_response": llm_response,
            "timestamp": json.dumps({"timestamp": "now"}, default=str),
            "session_id": session_id
        }
        
        self.conversation_history.append(conversation_entry)
        
        # Keep only last 10 conversations to manage memory
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]
        
        return {
            "response": llm_response,
            "session_id": session_id,
            "timestamp": conversation_entry["timestamp"]
        }
    
    def get_conversation_history(self, session_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get conversation history for a session"""
        if session_id:
            return [entry for entry in self.conversation_history if entry.get("session_id") == session_id]
        return self.conversation_history
    
    def clear_conversation_history(self, session_id: Optional[str] = None):
        """Clear conversation history"""
        if session_id:
            self.conversation_history = [entry for entry in self.conversation_history if entry.get("session_id") != session_id]
        else:
            self.conversation_history = []
    
    def get_suggested_questions(self) -> List[str]:
        """Get a list of suggested questions users can ask"""
        return [
            "How many database servers do we have?",
            "What types of databases are in our inventory?",
            "Which servers are running PostgreSQL?",
            "Show me servers with the most CPU cores",
            "What operating systems are we using?",
            "Which business units have the most applications?",
            "List all Oracle database servers",
            "What applications does the IT business unit own?",
            "Show me servers with less than 16 CPU cores",
            "Which servers are running Windows?",
            "What's the total RAM across all servers?",
            "How many unique applications do we have?"
        ]

# Global instance
chat_agent = CMDBChatAgent()

def process_chat_message(message: str, session_id: Optional[str] = None) -> Dict[str, Any]:
    """Convenience function to process a chat message"""
    return chat_agent.chat(message, session_id)

def get_chat_suggestions() -> List[str]:
    """Convenience function to get suggested questions"""
    return chat_agent.get_suggested_questions()
