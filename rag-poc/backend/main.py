"""
CMDB Management API
FastAPI backend for Configuration Management Database operations
"""
from fastapi import FastAPI, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import logging
from cmdb_reader import cmdb_reader, get_cmdb_data, search_cmdb
from cmdb_obsolescence_agent import obsolescence_agent, get_obsolete_servers
from cmdb_chat_agent import process_chat_message, get_chat_suggestions

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')

app = FastAPI(
    title="CMDB Management API",
    description="Configuration Management Database with AI-powered analysis",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"]
)


# CMDB Data Models
class ChatMessage(BaseModel):
    message: str
    session_id: str = None


# Health check endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "CMDB Management API",
        "version": "1.0.0",
        "endpoints": {
            "data": "/cmdb/data",
            "servers": "/cmdb/servers", 
            "apps": "/cmdb/apps",
            "search": "/cmdb/search?q=query",
            "obsolete": "/cmdb/obsolete",
            "chat": "/cmdb/chat",
            "stats": "/cmdb/stats"
        }
    }


# CMDB Endpoints
@app.get("/cmdb/data")
async def get_cmdb_data_endpoint():
    """Get all CMDB data"""
    try:
        data = get_cmdb_data()
        return {
            "status": "success",
            "data": data,
            "summary": cmdb_reader.get_summary_stats()
        }
    except Exception as e:
        logging.error(f"Error fetching CMDB data: {e}")
        return {"status": "error", "message": str(e)}


@app.get("/cmdb/servers")
async def get_database_servers():
    """Get all database servers"""
    try:
        servers = cmdb_reader.get_database_servers()
        return {
            "status": "success",
            "servers": servers,
            "count": len(servers)
        }
    except Exception as e:
        logging.error(f"Error fetching database servers: {e}")
        return {"status": "error", "message": str(e)}


@app.get("/cmdb/apps")
async def get_app_mappings():
    """Get all application mappings"""
    try:
        apps = cmdb_reader.get_app_mappings()
        return {
            "status": "success",
            "applications": apps,
            "count": len(apps)
        }
    except Exception as e:
        logging.error(f"Error fetching app mappings: {e}")
        return {"status": "error", "message": str(e)}


@app.get("/cmdb/search")
async def search_cmdb_data(q: str = Query(..., description="Search query")):
    """Search CMDB data"""
    try:
        results = search_cmdb(q)
        return {
            "status": "success",
            "query": q,
            "results": results
        }
    except Exception as e:
        logging.error(f"Error searching CMDB data: {e}")
        return {"status": "error", "message": str(e)}


@app.get("/cmdb/obsolete")
async def get_obsolete_entries(
    risk_threshold: int = Query(70, description="Risk threshold (0-100)")
):
    """Get potentially obsolete CMDB entries"""
    try:
        obsolete_servers = get_obsolete_servers(risk_threshold)
        summary = obsolescence_agent.get_obsolescence_summary()
        
        return {
            "status": "success",
            "risk_threshold": risk_threshold,
            "obsolete_servers": obsolete_servers,
            "summary": summary
        }
    except Exception as e:
        logging.error(f"Error analyzing obsolescence: {e}")
        return {"status": "error", "message": str(e)}


@app.post("/cmdb/chat")
async def chat_with_cmdb(chat_message: ChatMessage):
    """Chat interface for CMDB data"""
    try:
        response = process_chat_message(
            chat_message.message, 
            chat_message.session_id
        )
        return {
            "status": "success",
            "response": response["response"],
            "session_id": response["session_id"],
            "timestamp": response["timestamp"]
        }
    except Exception as e:
        logging.error(f"Error in CMDB chat: {e}")
        return {"status": "error", "message": str(e)}


@app.get("/cmdb/chat/suggestions")
async def get_chat_suggestions_endpoint():
    """Get suggested questions for CMDB chat"""
    try:
        suggestions = get_chat_suggestions()
        return {
            "status": "success",
            "suggestions": suggestions
        }
    except Exception as e:
        logging.error(f"Error getting chat suggestions: {e}")
        return {"status": "error", "message": str(e)}


@app.get("/cmdb/stats")
async def get_cmdb_stats():
    """Get CMDB summary statistics"""
    try:
        stats = cmdb_reader.get_summary_stats()
        return {
            "status": "success",
            "statistics": stats
        }
    except Exception as e:
        logging.error(f"Error getting CMDB stats: {e}")
        return {"status": "error", "message": str(e)}


# Server info endpoint
@app.get("/cmdb/server/{hostname}")
async def get_server_by_hostname(hostname: str):
    """Get specific server by hostname"""
    try:
        server = cmdb_reader.get_server_by_hostname(hostname)
        if server:
            return {
                "status": "success",
                "server": server
            }
        else:
            return {
                "status": "not_found",
                "message": f"Server with hostname '{hostname}' not found"
            }
    except Exception as e:
        logging.error(f"Error fetching server {hostname}: {e}")
        return {"status": "error", "message": str(e)}


# Database type filtering
@app.get("/cmdb/servers/database/{db_type}")
async def get_servers_by_database(db_type: str):
    """Get servers by database type"""
    try:
        servers = cmdb_reader.get_servers_by_database_type(db_type)
        return {
            "status": "success",
            "database_type": db_type,
            "servers": servers,
            "count": len(servers)
        }
    except Exception as e:
        logging.error(f"Error fetching {db_type} servers: {e}")
        return {"status": "error", "message": str(e)}


# Operating system filtering
@app.get("/cmdb/servers/os/{os_name}")
async def get_servers_by_os(os_name: str):
    """Get servers by operating system"""
    try:
        servers = cmdb_reader.get_servers_by_os(os_name)
        return {
            "status": "success",
            "operating_system": os_name,
            "servers": servers,
            "count": len(servers)
        }
    except Exception as e:
        logging.error(f"Error fetching {os_name} servers: {e}")
        return {"status": "error", "message": str(e)}


# Business unit filtering
@app.get("/cmdb/apps/business-unit/{business_unit}")
async def get_apps_by_business_unit(business_unit: str):
    """Get applications by business unit"""
    try:
        apps = cmdb_reader.get_apps_by_business_unit(business_unit)
        return {
            "status": "success",
            "business_unit": business_unit,
            "applications": apps,
            "count": len(apps)
        }
    except Exception as e:
        logging.error(f"Error fetching {business_unit} apps: {e}")
        return {"status": "error", "message": str(e)}
