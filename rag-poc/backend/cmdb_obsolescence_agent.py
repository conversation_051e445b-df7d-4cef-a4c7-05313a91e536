"""
CMDB Obsolescence Detection Agent
Uses LLM to analyze CMDB data and identify potentially obsolete entries
"""
import requests
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from cmdb_reader import cmdb_reader

logger = logging.getLogger(__name__)

class CMDBObsolescenceAgent:
    """Agent to detect obsolete CMDB entries using LLM analysis"""
    
    def __init__(self, model_url: str = "http://localhost:11434/api/generate", model_name: str = "llava"):
        self.model_url = model_url
        self.model_name = model_name
        
    def _call_llm(self, prompt: str) -> str:
        """Call the LLM with a prompt and return the response"""
        try:
            response = requests.post(
                self.model_url,
                json={
                    "model": self.model_name,
                    "prompt": prompt,
                    "stream": False
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "").strip()
            else:
                logger.error(f"LLM API error: {response.status_code}")
                return ""
                
        except Exception as e:
            logger.error(f"Error calling LLM: {e}")
            return ""
    
    def analyze_database_server_obsolescence(self, server: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze a database server for obsolescence indicators"""
        
        prompt = f"""
        Analyze this database server configuration for obsolescence indicators:
        
        Server Details:
        - Hostname: {server.get('Hostname', 'N/A')}
        - Database: {server.get('Database', 'N/A')} {server.get('DB Version', 'N/A')}
        - Operating System: {server.get('Operating System', 'N/A')} {server.get('OS Version', 'N/A')}
        - CPU: {server.get('CPU', 'N/A')} ({server.get('CPU Cores', 'N/A')} cores)
        - RAM: {server.get('RAM', 'N/A')}
        - Storage: {server.get('Memory', 'N/A')}
        
        Evaluate this server for obsolescence based on:
        1. Database version age (consider current versions and support lifecycle)
        2. Operating system version age and support status
        3. Hardware specifications (CPU, RAM) relative to modern standards
        4. Overall technology stack modernness
        
        Provide your analysis in this JSON format:
        {{
            "obsolescence_risk": "low|medium|high",
            "risk_score": 0-100,
            "primary_concerns": ["concern1", "concern2"],
            "recommendations": ["recommendation1", "recommendation2"],
            "reasoning": "Brief explanation of the assessment"
        }}
        
        Respond only with valid JSON.
        """
        
        llm_response = self._call_llm(prompt)
        
        try:
            # Try to parse JSON response
            analysis = json.loads(llm_response)
            
            # Add metadata
            analysis['server_id'] = server.get('Asset ID', 'Unknown')
            analysis['hostname'] = server.get('Hostname', 'Unknown')
            analysis['analyzed_at'] = datetime.now().isoformat()
            
            return analysis
            
        except json.JSONDecodeError:
            logger.error(f"Failed to parse LLM response as JSON: {llm_response}")
            # Return a fallback analysis
            return {
                "obsolescence_risk": "unknown",
                "risk_score": 50,
                "primary_concerns": ["Unable to analyze - LLM response parsing failed"],
                "recommendations": ["Manual review required"],
                "reasoning": "LLM analysis failed",
                "server_id": server.get('Asset ID', 'Unknown'),
                "hostname": server.get('Hostname', 'Unknown'),
                "analyzed_at": datetime.now().isoformat(),
                "raw_llm_response": llm_response
            }
    
    def analyze_all_database_servers(self) -> List[Dict[str, Any]]:
        """Analyze all database servers for obsolescence"""
        servers = cmdb_reader.get_database_servers()
        results = []
        
        logger.info(f"Analyzing {len(servers)} database servers for obsolescence")
        
        for i, server in enumerate(servers):
            logger.info(f"Analyzing server {i+1}/{len(servers)}: {server.get('Hostname', 'Unknown')}")
            analysis = self.analyze_database_server_obsolescence(server)
            results.append(analysis)
        
        return results
    
    def get_high_risk_servers(self, threshold: int = 70) -> List[Dict[str, Any]]:
        """Get servers with high obsolescence risk"""
        all_analyses = self.analyze_all_database_servers()
        return [a for a in all_analyses if a.get('risk_score', 0) >= threshold]
    
    def get_obsolescence_summary(self) -> Dict[str, Any]:
        """Get a summary of obsolescence analysis across all servers"""
        analyses = self.analyze_all_database_servers()
        
        if not analyses:
            return {"error": "No servers analyzed"}
        
        # Calculate statistics
        risk_scores = [a.get('risk_score', 0) for a in analyses]
        risk_levels = [a.get('obsolescence_risk', 'unknown') for a in analyses]
        
        summary = {
            "total_servers": len(analyses),
            "average_risk_score": sum(risk_scores) / len(risk_scores) if risk_scores else 0,
            "risk_distribution": {
                "high": len([r for r in risk_levels if r == 'high']),
                "medium": len([r for r in risk_levels if r == 'medium']),
                "low": len([r for r in risk_levels if r == 'low']),
                "unknown": len([r for r in risk_levels if r == 'unknown'])
            },
            "top_concerns": self._get_top_concerns(analyses),
            "analyzed_at": datetime.now().isoformat()
        }
        
        return summary
    
    def _get_top_concerns(self, analyses: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract and count the most common concerns"""
        concern_counts = {}
        
        for analysis in analyses:
            concerns = analysis.get('primary_concerns', [])
            for concern in concerns:
                concern_counts[concern] = concern_counts.get(concern, 0) + 1
        
        # Sort by frequency and return top 5
        sorted_concerns = sorted(concern_counts.items(), key=lambda x: x[1], reverse=True)
        return [{"concern": concern, "count": count} for concern, count in sorted_concerns[:5]]

# Global instance
obsolescence_agent = CMDBObsolescenceAgent()

def analyze_server_obsolescence(server_data: Dict[str, Any]) -> Dict[str, Any]:
    """Convenience function to analyze a single server"""
    return obsolescence_agent.analyze_database_server_obsolescence(server_data)

def get_obsolete_servers(risk_threshold: int = 70) -> List[Dict[str, Any]]:
    """Convenience function to get high-risk servers"""
    return obsolescence_agent.get_high_risk_servers(risk_threshold)
