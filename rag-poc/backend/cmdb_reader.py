"""
CMDB Data Reader Module
Handles reading and processing CMDB data from Excel files
"""
import os
import json
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class CMDBReader:
    """Class to handle CMDB data operations"""
    
    def __init__(self, cmdb_dir: str = "cmdb_data"):
        self.cmdb_dir = cmdb_dir
        self.data_cache = {}
        self.last_loaded = None
        
    def load_all_data(self, force_reload: bool = False) -> Dict[str, List[Dict]]:
        """Load all CMDB data from Excel files"""
        if not force_reload and self.data_cache and self.last_loaded:
            # Return cached data if available and recent
            time_diff = datetime.now() - self.last_loaded
            if time_diff.total_seconds() < 300:  # 5 minutes cache
                return self.data_cache
        
        self.data_cache = {}
        
        if not os.path.exists(self.cmdb_dir):
            logger.error(f"CMDB directory {self.cmdb_dir} not found")
            return {}
        
        # Load database server inventory
        db_file = os.path.join(self.cmdb_dir, "Database_server_inventory_dummy_cmdb.xlsx")
        if os.path.exists(db_file):
            try:
                df = pd.read_excel(db_file)
                self.data_cache['database_servers'] = df.to_dict('records')
                logger.info(f"Loaded {len(df)} database server records")
            except Exception as e:
                logger.error(f"Error loading database servers: {e}")
        
        # Load app CIO team mapping
        app_file = os.path.join(self.cmdb_dir, "App_CIO_team_mapping_v0.1.xlsx")
        if os.path.exists(app_file):
            try:
                df = pd.read_excel(app_file)
                self.data_cache['app_mappings'] = df.to_dict('records')
                logger.info(f"Loaded {len(df)} app mapping records")
            except Exception as e:
                logger.error(f"Error loading app mappings: {e}")
        
        self.last_loaded = datetime.now()
        return self.data_cache
    
    def get_database_servers(self) -> List[Dict]:
        """Get all database server records"""
        data = self.load_all_data()
        return data.get('database_servers', [])
    
    def get_app_mappings(self) -> List[Dict]:
        """Get all application mapping records"""
        data = self.load_all_data()
        return data.get('app_mappings', [])
    
    def search_servers(self, query: str) -> List[Dict]:
        """Search database servers by hostname, database type, or OS"""
        servers = self.get_database_servers()
        query_lower = query.lower()
        
        results = []
        for server in servers:
            # Search in key fields
            searchable_fields = ['Hostname', 'Database', 'Operating System', 'OS Version']
            for field in searchable_fields:
                if field in server and query_lower in str(server[field]).lower():
                    results.append(server)
                    break
        
        return results
    
    def get_server_by_hostname(self, hostname: str) -> Optional[Dict]:
        """Get a specific server by hostname"""
        servers = self.get_database_servers()
        for server in servers:
            if server.get('Hostname', '').lower() == hostname.lower():
                return server
        return None
    
    def get_servers_by_database_type(self, db_type: str) -> List[Dict]:
        """Get servers by database type (PostgreSQL, Oracle, etc.)"""
        servers = self.get_database_servers()
        return [s for s in servers if s.get('Database', '').lower() == db_type.lower()]
    
    def get_servers_by_os(self, os_name: str) -> List[Dict]:
        """Get servers by operating system"""
        servers = self.get_database_servers()
        return [s for s in servers if os_name.lower() in s.get('Operating System', '').lower()]
    
    def get_apps_by_business_unit(self, business_unit: str) -> List[Dict]:
        """Get applications by business unit"""
        apps = self.get_app_mappings()
        return [a for a in apps if a.get('Business Unit', '').lower() == business_unit.lower()]
    
    def get_unique_values(self, dataset: str, field: str) -> List[str]:
        """Get unique values for a specific field in a dataset"""
        if dataset == 'database_servers':
            data = self.get_database_servers()
        elif dataset == 'app_mappings':
            data = self.get_app_mappings()
        else:
            return []
        
        values = set()
        for record in data:
            if field in record and record[field] is not None:
                values.add(str(record[field]))
        
        return sorted(list(values))
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Get summary statistics for all datasets"""
        db_servers = self.get_database_servers()
        app_mappings = self.get_app_mappings()
        
        stats = {
            'database_servers': {
                'total_count': len(db_servers),
                'database_types': self.get_unique_values('database_servers', 'Database'),
                'operating_systems': self.get_unique_values('database_servers', 'Operating System'),
                'total_cpu_cores': sum(s.get('CPU Cores', 0) for s in db_servers if isinstance(s.get('CPU Cores'), int))
            },
            'app_mappings': {
                'total_count': len(app_mappings),
                'business_units': self.get_unique_values('app_mappings', 'Business Unit'),
                'unique_applications': len(self.get_unique_values('app_mappings', 'Mapped Application'))
            }
        }
        
        return stats

# Global instance
cmdb_reader = CMDBReader()

def get_cmdb_data() -> Dict[str, List[Dict]]:
    """Convenience function to get all CMDB data"""
    return cmdb_reader.load_all_data()

def search_cmdb(query: str) -> Dict[str, List[Dict]]:
    """Search across all CMDB datasets"""
    return {
        'database_servers': cmdb_reader.search_servers(query),
        'app_mappings': [a for a in cmdb_reader.get_app_mappings() 
                        if query.lower() in str(a.get('Mapped Application', '')).lower() or
                           query.lower() in str(a.get('Business Unit', '')).lower()]
    }
