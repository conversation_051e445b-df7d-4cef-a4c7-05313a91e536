#!/usr/bin/env python3
"""
Simple test script for CMDB functionality without complex dependencies
"""
import sys
import os

# Try to import pandas, install if not available
try:
    import pandas as pd
except ImportError:
    print("Installing pandas and openpyxl...")
    import subprocess
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pandas', 'openpyxl'])
    import pandas as pd

def test_cmdb_reader():
    """Test the CMDB reader functionality"""
    print("Testing CMDB Reader...")
    
    # Import our modules
    from cmdb_reader import CMDBReader
    
    # Create reader instance
    reader = CMDBReader()
    
    # Test loading data
    print("Loading CMDB data...")
    data = reader.load_all_data()
    
    print(f"Loaded datasets: {list(data.keys())}")
    
    if 'database_servers' in data:
        print(f"Database servers: {len(data['database_servers'])}")
        if data['database_servers']:
            print("Sample server:", data['database_servers'][0])
    
    if 'app_mappings' in data:
        print(f"App mappings: {len(data['app_mappings'])}")
        if data['app_mappings']:
            print("Sample app:", data['app_mappings'][0])
    
    # Test search
    print("\nTesting search...")
    search_results = reader.search_servers("PostgreSQL")
    print(f"PostgreSQL servers found: {len(search_results)}")
    
    # Test stats
    print("\nTesting stats...")
    stats = reader.get_summary_stats()
    print("Summary stats:", stats)
    
    print("CMDB Reader test completed successfully!")

def test_simple_api():
    """Test a simple API without LLM dependencies"""
    print("\nTesting simple API endpoints...")
    
    try:
        from fastapi import FastAPI
        from cmdb_reader import cmdb_reader
        
        app = FastAPI()
        
        @app.get("/test/cmdb/data")
        async def get_test_data():
            data = cmdb_reader.load_all_data()
            return {
                "status": "success",
                "data_keys": list(data.keys()),
                "server_count": len(data.get('database_servers', [])),
                "app_count": len(data.get('app_mappings', []))
            }
        
        print("Simple API test setup completed!")
        print("You can run: uvicorn test_cmdb_simple:app --reload --port 8004")
        
    except ImportError as e:
        print(f"FastAPI not available: {e}")

if __name__ == "__main__":
    test_cmdb_reader()
    test_simple_api()
