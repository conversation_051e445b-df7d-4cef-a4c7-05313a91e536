#!/usr/bin/env python3
"""
Script to examine CMDB data structure
"""
import sys
import os

# Try to import pandas, install if not available
try:
    import pandas as pd
except ImportError:
    print("Installing pandas and openpyxl...")
    import subprocess
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pandas', 'openpyxl'])
    import pandas as pd

def examine_cmdb_files():
    """Examine the structure of CMDB Excel files"""
    cmdb_dir = 'cmdb_data'
    
    if not os.path.exists(cmdb_dir):
        print(f"Directory {cmdb_dir} not found!")
        return
    
    files = [f for f in os.listdir(cmdb_dir) if f.endswith('.xlsx')]
    print(f"Found CMDB files: {files}")
    
    for file in files:
        filepath = os.path.join(cmdb_dir, file)
        print(f"\n{'='*60}")
        print(f"Examining: {file}")
        print(f"{'='*60}")
        
        try:
            # Read the Excel file
            df = pd.read_excel(filepath)
            
            print(f"Shape: {df.shape} (rows x columns)")
            print(f"Columns: {list(df.columns)}")
            
            # Show data types
            print("\nData types:")
            for col, dtype in df.dtypes.items():
                print(f"  {col}: {dtype}")
            
            # Show first few rows
            print(f"\nFirst 3 rows:")
            print(df.head(3).to_string(max_cols=10))
            
            # Show some statistics
            print(f"\nBasic statistics:")
            print(f"  Non-null values per column:")
            for col in df.columns:
                non_null = df[col].notna().sum()
                print(f"    {col}: {non_null}/{len(df)} ({non_null/len(df)*100:.1f}%)")
            
            # Check for potential date columns
            date_like_cols = []
            for col in df.columns:
                if any(keyword in col.lower() for keyword in ['date', 'time', 'created', 'updated', 'modified']):
                    date_like_cols.append(col)
            
            if date_like_cols:
                print(f"\nPotential date columns: {date_like_cols}")
                for col in date_like_cols:
                    print(f"  {col} sample values: {df[col].dropna().head(3).tolist()}")
            
            print(f"\nData successfully loaded from Excel file: {file}")
            
        except Exception as e:
            print(f"Error reading {file}: {e}")

if __name__ == "__main__":
    examine_cmdb_files()
