# CMDB Integration Setup Guide

## Overview

This project has been successfully extended to include comprehensive CMDB (Configuration Management Database) functionality alongside the existing KYC verification system. The new features include:

- **CMDB Data Management**: Read and display database server inventory and application mappings
- **LLM-Powered Obsolescence Detection**: AI analysis to identify potentially obsolete infrastructure
- **Interactive Chat Interface**: Ask questions about your CMDB data using natural language
- **Modern React UI**: Responsive interface with data tables, search, and analytics

## What's Been Added

### Backend Components

1. **CMDB Data Reader** (`cmdb_reader.py`)
   - Reads Excel files from `cmdb_data/` directory
   - Provides search and filtering capabilities
   - Caches data for performance

2. **Obsolescence Detection Agent** (`cmdb_obsolescence_agent.py`)
   - Uses LLM to analyze server configurations
   - Identifies outdated technology stacks
   - Provides risk scores and recommendations

3. **Chat Agent** (`cmdb_chat_agent.py`)
   - Natural language interface for CMDB queries
   - Context-aware responses using CMDB data
   - Conversation history management

4. **API Endpoints** (added to `main.py`)
   - `/cmdb/data` - Get all CMDB data
   - `/cmdb/servers` - Database servers only
   - `/cmdb/apps` - Application mappings only
   - `/cmdb/search?q=query` - Search across data
   - `/cmdb/obsolete` - Obsolescence analysis
   - `/cmdb/chat` - Chat interface
   - `/cmdb/stats` - Summary statistics

### Frontend Components

1. **CMDB Data Table** (`CMDBDataTable.tsx`)
   - Tabbed interface for servers, apps, and obsolescence analysis
   - Search functionality
   - Risk assessment visualization

2. **CMDB Chat Interface** (`CMDBChat.tsx`)
   - Real-time chat with AI assistant
   - Suggested questions
   - Message history

3. **CMDB Dashboard** (`CMDBDashboard.tsx`)
   - Main page combining all CMDB features
   - Tab-based navigation
   - Analytics placeholder

4. **Updated Navigation**
   - Added CMDB Dashboard to sidebar
   - Updated home page with CMDB links
   - Modified header title

## Data Structure

### Database Server Inventory
- 100 sample servers with complete specifications
- Fields: Asset ID, Hostname, Database type/version, OS, CPU, RAM, IPs, etc.
- Multiple database types: PostgreSQL, Oracle, MySQL, MongoDB, SQL Server
- Various operating systems: Linux, Windows Server, Unix, Red Hat

### Application Mappings
- 56 application records
- Fields: Application name, Business owner email, Business unit
- 6 business units: Operations, IT, Marketing, Finance, HR, Sales
- 12 unique applications

## Setup Instructions

### Prerequisites
- Node.js (v18+)
- Python 3.9+
- Ollama with llava model (for LLM features)

### Backend Setup

1. **Install Dependencies**
   ```bash
   cd san-poc/backend
   pip install -r requirements.txt
   ```

2. **Test CMDB Functionality**
   ```bash
   python test_cmdb_simple.py
   ```

3. **Start Backend Server**
   ```bash
   uvicorn main:app --reload --port 8003
   ```

### Frontend Setup

1. **Install Dependencies**
   ```bash
   cd san-poc/frontend
   npm install
   ```

2. **Start Development Server**
   ```bash
   npm run dev
   ```

### Ollama Setup (for LLM features)

1. **Install and Start Ollama**
   ```bash
   ollama pull llava
   ollama serve
   ```

## Usage Guide

### Accessing CMDB Features

1. **Navigate to CMDB Dashboard**
   - Open http://localhost:5173
   - Click "CMDB Dashboard" in sidebar or home page

2. **View Data**
   - "CMDB Data" tab shows servers and applications
   - Use search to filter results
   - Click "Analyze Obsolescence" for AI analysis

3. **Chat with AI**
   - "AI Assistant" tab provides natural language interface
   - Try suggested questions or ask your own
   - Examples:
     - "How many PostgreSQL servers do we have?"
     - "Which servers have the most CPU cores?"
     - "Show me applications owned by the IT business unit"

4. **Obsolescence Analysis**
   - Analyzes technology stack age and support status
   - Provides risk scores (0-100)
   - Lists specific concerns and recommendations
   - Adjustable risk threshold

## API Testing

Test the CMDB endpoints directly:

```bash
# Get all data
curl http://localhost:8003/cmdb/data

# Search for PostgreSQL
curl "http://localhost:8003/cmdb/search?q=PostgreSQL"

# Get obsolescence analysis
curl "http://localhost:8003/cmdb/obsolete?risk_threshold=70"

# Chat with AI
curl -X POST http://localhost:8003/cmdb/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "How many servers do we have?"}'
```

## Troubleshooting

### Common Issues

1. **Dependency Conflicts**
   - If you encounter langgraph/langchain conflicts, try installing packages individually
   - The core CMDB functionality works without LLM dependencies

2. **Ollama Connection**
   - Ensure Ollama is running on port 11434
   - Check that llava model is downloaded
   - LLM features will gracefully degrade if Ollama is unavailable

3. **Excel File Issues**
   - Ensure CMDB Excel files are in `backend/cmdb_data/` directory
   - Files should be readable by pandas/openpyxl

### Testing Without LLM

If you want to test without Ollama/LLM features:

```bash
cd san-poc/backend
python test_cmdb_simple.py
```

This will test the core CMDB data reading functionality.

## Next Steps

The foundation is now in place for:
- Advanced analytics and reporting
- Integration with real CMDB systems
- Enhanced obsolescence detection algorithms
- Custom business rules and workflows
- Export and reporting features

All components are modular and can be extended based on your specific requirements.
