{
  "compilerOptions": {
    "target": "es6",
    "lib": ["dom", "dom.iterable", "es6"],
    "module": "esnext",
    "moduleResolution": "node",
    "jsx": "react-jsx",
    "strict": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "baseUrl": "src"
  },
  "include": ["src"],  // Ensure this includes all source code files
  "exclude": ["node_modules", "build", "dist"],  // Exclude unnecessary folders like build or dist
  "references": [
    { "path": "./tsconfig.app.json" },
    { "path": "./tsconfig.node.json" }
  ]
}

