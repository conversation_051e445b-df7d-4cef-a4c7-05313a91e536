{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2023"],
    "module": "ESNext",
    "moduleResolution": "node",
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowJs": true,
    "jsx": "react",
    "resolveJsonModule": true,
    "composite": true,  // Add composite true to allow referencing this project
    "declaration": true,  // Generate declaration files for the project
    "outDir": "./dist"    // Specify the output directory if needed
  },
  "include": [
    "src",
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}
