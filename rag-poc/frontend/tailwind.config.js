/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './src/**/*.{html,js,jsx,ts,tsx}',  // Ensure all your React and TypeScript files are scanned
  ],
  darkMode: 'class',  // Allows toggling dark mode by adding/removing the 'dark' class
  theme: {
    extend: {
      colors: {
        // primary: '#00338D',  // KPMG Blue (primary color)
        // secondary: '#005EB8',  // Medium Blue (secondary color)
        // accent: '#0091DA',  // Light Blue (accent color)
        // darkPrimary: '#00338D',  // KPMG Blue (dark mode primary color)
        // darkSecondary: '#005EB8',  // Medium Blue (dark mode secondary color)
        // backgroundLight: '#F5F5F5',  // Light background
        // backgroundDark: '#1A1A1A',  // Dark background

        kpmgDarkBlue: '#01338d',  // KPMG Dark Blue
        primary: '#00338D',  // KPMG Primary Blue (light mode)
        darkPrimary: '#01338d',  // Dark mode KPMG Primary Blue
        backgroundLight: '#F5F5F5',  // Light background color
        backgroundDark: '#1A1A1A',  // Dark background color
        kpmgMediumBlue: '#005EB8',  // KPMG Medium Blue
        kpmgLightBlue: '#0091DA',  // KPMG Light Blue
        kpmgDarkGray: '#333333',  // KPMG Dark Gray
        kpmgLightGray: '#F5F5F5',  // KPMG Light Gray
        kpmgWhite: '#FFFFFF',  // KPMG White
        kpmgBlack: '#000000',  // KPMG Black
        kpmgGray: '#808080',  // KPMG Gray
      },
    },
  },
  plugins: [],
}
