// export type KYCResult =
//   | { kyc_data: string; kyc_id: string }
//   | { error: string };

export type VerificationStatus = "pending" | "success" | "failed" | null;
// types.ts (or wherever your types are defined)

export interface KYCResult {
  status: string; // The status is always required, even in error cases
  message?: string; // Optional message (like 'Image Quality Issue')
  kyc_data?: string; // KYC data if the document is valid
  kyc_id?: string; // The KYC ID for tracking
  error?: string; // Error message for failure
}
