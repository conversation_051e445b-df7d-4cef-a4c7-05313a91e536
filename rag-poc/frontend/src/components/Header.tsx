import React, { useState, useEffect } from "react";
import { FaMoon, FaSun } from "react-icons/fa"; // Importing moon and sun icons

const Header = () => {
  const [darkMode, setDarkMode] = useState(false);

  // Check if dark mode was set previously in localStorage
  useEffect(() => {
    const storedDarkMode = localStorage.getItem("darkMode") === "true";
    setDarkMode(storedDarkMode);
    if (storedDarkMode) {
      document.documentElement.classList.add("dark");
    }
  }, []);

  // Toggle dark mode
  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    if (!darkMode) {
      document.documentElement.classList.add("dark");
      localStorage.setItem("darkMode", "true"); // Store dark mode preference in localStorage
    } else {
      document.documentElement.classList.remove("dark");
      localStorage.setItem("darkMode", "false"); // Store light mode preference in localStorage
    }
  };

  return (
    <header className="bg-kpmgDarkBlue text-white p-6 flex justify-between items-center">
      <h1 className="text-2xl font-bold">CMDB Management System</h1>
      <button
        onClick={toggleDarkMode}
        className="p-2 bg-transparent text-white rounded-full hover:bg-gray-700 focus:outline-none"
      >
        {darkMode ? (
          <FaSun size={24} /> // Sun icon when dark mode is on
        ) : (
          <FaMoon size={24} /> // Moon icon when dark mode is off
        )}
      </button>
    </header>
  );
};

export default Header;
