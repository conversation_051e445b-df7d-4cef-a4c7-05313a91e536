import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaServer, FaDatabase, FaSearch, FaExclamationTriangle } from 'react-icons/fa';

interface DatabaseServer {
  'Asset ID': string;
  'Hostname': string;
  'Database': string;
  'DB Version': string;
  'Service IP': string;
  'Admin IP': string;
  'Service FQDN': string;
  'Admin FQDN': string;
  'Operating System': string;
  'OS Version': string;
  'CPU': string;
  'CPU Cores': number;
  'RAM': string;
  'Memory': string;
}

interface AppMapping {
  'Mapped Application': string;
  'CIO/Business Owner Mail': string;
  'Business Unit': string;
}

interface CMDBData {
  database_servers: DatabaseServer[];
  app_mappings: AppMapping[];
}

interface ObsoleteServer {
  obsolescence_risk: 'low' | 'medium' | 'high';
  risk_score: number;
  primary_concerns: string[];
  recommendations: string[];
  reasoning: string;
  server_id: string;
  hostname: string;
  analyzed_at: string;
}

const CMDBDataTable: React.FC = () => {
  const [cmdbData, setCmdbData] = useState<CMDBData | null>(null);
  const [obsoleteServers, setObsoleteServers] = useState<ObsoleteServer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'servers' | 'apps' | 'obsolete'>('servers');
  const [searchTerm, setSearchTerm] = useState('');
  const [riskThreshold, setRiskThreshold] = useState(70);

  useEffect(() => {
    fetchCMDBData();
  }, []);

  const fetchCMDBData = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:8003/cmdb/data');
      if (response.data.status === 'success') {
        setCmdbData(response.data.data);
      } else {
        setError(response.data.message || 'Failed to fetch CMDB data');
      }
    } catch (err) {
      setError('Error connecting to backend');
      console.error('Error fetching CMDB data:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchObsoleteServers = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`http://localhost:8003/cmdb/obsolete?risk_threshold=${riskThreshold}`);
      if (response.data.status === 'success') {
        setObsoleteServers(response.data.obsolete_servers);
      } else {
        setError(response.data.message || 'Failed to fetch obsolete servers');
      }
    } catch (err) {
      setError('Error analyzing obsolete servers');
      console.error('Error fetching obsolete servers:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleObsoleteAnalysis = () => {
    setActiveTab('obsolete');
    fetchObsoleteServers();
  };

  const filteredServers = cmdbData?.database_servers.filter(server =>
    Object.values(server).some(value =>
      value?.toString().toLowerCase().includes(searchTerm.toLowerCase())
    )
  ) || [];

  const filteredApps = cmdbData?.app_mappings.filter(app =>
    Object.values(app).some(value =>
      value?.toString().toLowerCase().includes(searchTerm.toLowerCase())
    )
  ) || [];

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-kpmgDarkBlue"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <strong>Error:</strong> {error}
        <button
          onClick={fetchCMDBData}
          className="ml-4 bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-4 text-kpmgDarkBlue">CMDB Data Management</h2>
        
        {/* Search and Controls */}
        <div className="flex flex-wrap gap-4 mb-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <FaSearch className="absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                placeholder="Search CMDB data..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kpmgDarkBlue focus:border-transparent"
              />
            </div>
          </div>
          
          <button
            onClick={handleObsoleteAnalysis}
            className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 flex items-center gap-2"
          >
            <FaExclamationTriangle />
            Analyze Obsolescence
          </button>
          
          {activeTab === 'obsolete' && (
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium">Risk Threshold:</label>
              <input
                type="range"
                min="0"
                max="100"
                value={riskThreshold}
                onChange={(e) => setRiskThreshold(Number(e.target.value))}
                className="w-20"
              />
              <span className="text-sm">{riskThreshold}%</span>
            </div>
          )}
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 mb-4">
          <button
            onClick={() => setActiveTab('servers')}
            className={`px-4 py-2 font-medium ${
              activeTab === 'servers'
                ? 'border-b-2 border-kpmgDarkBlue text-kpmgDarkBlue'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <FaServer className="inline mr-2" />
            Database Servers ({cmdbData?.database_servers.length || 0})
          </button>
          <button
            onClick={() => setActiveTab('apps')}
            className={`px-4 py-2 font-medium ${
              activeTab === 'apps'
                ? 'border-b-2 border-kpmgDarkBlue text-kpmgDarkBlue'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <FaDatabase className="inline mr-2" />
            Applications ({cmdbData?.app_mappings.length || 0})
          </button>
          <button
            onClick={() => setActiveTab('obsolete')}
            className={`px-4 py-2 font-medium ${
              activeTab === 'obsolete'
                ? 'border-b-2 border-kpmgDarkBlue text-kpmgDarkBlue'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <FaExclamationTriangle className="inline mr-2" />
            Obsolete Analysis ({obsoleteServers.length})
          </button>
        </div>
      </div>

      {/* Content based on active tab */}
      {activeTab === 'servers' && (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset ID</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hostname</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Database</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">OS</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CPU Cores</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">RAM</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service IP</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredServers.map((server, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{server['Asset ID']}</td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{server['Hostname']}</td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    {server['Database']} {server['DB Version']}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    {server['Operating System']} {server['OS Version']}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{server['CPU Cores']}</td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{server['RAM']}</td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{server['Service IP']}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {activeTab === 'apps' && (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Application</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Owner</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Business Unit</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredApps.map((app, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{app['Mapped Application']}</td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{app['CIO/Business Owner Mail']}</td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">{app['Business Unit']}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {activeTab === 'obsolete' && (
        <div className="space-y-4">
          {obsoleteServers.map((server, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{server.hostname}</h3>
                  <p className="text-sm text-gray-600">Asset ID: {server.server_id}</p>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRiskColor(server.obsolescence_risk)}`}>
                    {server.obsolescence_risk.toUpperCase()} RISK
                  </span>
                  <span className="text-lg font-bold text-gray-900">{server.risk_score}%</span>
                </div>
              </div>
              
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Primary Concerns:</h4>
                  <ul className="list-disc list-inside text-sm text-gray-700">
                    {server.primary_concerns.map((concern, idx) => (
                      <li key={idx}>{concern}</li>
                    ))}
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Recommendations:</h4>
                  <ul className="list-disc list-inside text-sm text-gray-700">
                    {server.recommendations.map((rec, idx) => (
                      <li key={idx}>{rec}</li>
                    ))}
                  </ul>
                </div>
              </div>
              
              <div className="mt-3 pt-3 border-t border-gray-200">
                <p className="text-sm text-gray-600">{server.reasoning}</p>
                <p className="text-xs text-gray-500 mt-1">
                  Analyzed: {new Date(server.analyzed_at).toLocaleString()}
                </p>
              </div>
            </div>
          ))}
          
          {obsoleteServers.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No servers found above the {riskThreshold}% risk threshold.
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CMDBDataTable;
