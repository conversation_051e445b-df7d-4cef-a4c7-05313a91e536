import React from "react";
import Header from "./Header";
import Sidebar from "./Sidebar";
// import Footer from "./Footer";

interface WrapperProps {
  children: React.ReactNode;
}

const Wrapper: React.FC<WrapperProps> = ({ children }) => {
  return (
    <div className="flex flex-col min-h-screen bg-backgroundLight dark:bg-backgroundDark text-primary dark:text-darkPrimary">
      {/* Header with Dark KPMG Blue Background */}
      <Header />

      <div className="flex flex-1">
        {/* Sidebar */}
        <Sidebar />

        {/* Main Content */}
        <main className="flex-1 p-4 bg-backgroundLight dark:bg-backgroundDark">
          {children}
        </main>
      </div>

      {/* Optional Footer */}
      {/* <Footer /> */}
    </div>
  );
};

export default Wrapper;
