import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { FaPaperPlane, FaRobot, FaUser, <PERSON>a<PERSON><PERSON>bulb, FaSpinner } from 'react-icons/fa';

interface ChatMessage {
  id: string;
  message: string;
  response?: string;
  timestamp: string;
  isUser: boolean;
}



const CMDBChat: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [sessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchSuggestions();
    // Add welcome message
    setMessages([{
      id: 'welcome',
      message: 'Welcome to CMDB Assistant! I can help you explore and analyze your CMDB data. Ask me anything about your database servers, applications, or infrastructure.',
      timestamp: new Date().toISOString(),
      isUser: false
    }]);
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchSuggestions = async () => {
    try {
      const response = await axios.get('http://localhost:8003/cmdb/chat/suggestions');
      if (response.data.status === 'success') {
        setSuggestions(response.data.suggestions);
      }
    } catch (error) {
      console.error('Error fetching suggestions:', error);
    }
  };

  const sendMessage = async (messageText: string) => {
    if (!messageText.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      message: messageText,
      timestamp: new Date().toISOString(),
      isUser: true
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response = await axios.post('http://localhost:8003/cmdb/chat', {
        message: messageText,
        session_id: sessionId
      });

      if (response.data.status === 'success') {
        const assistantMessage: ChatMessage = {
          id: `assistant_${Date.now()}`,
          message: response.data.response,
          timestamp: response.data.timestamp,
          isUser: false
        };

        setMessages(prev => [...prev, assistantMessage]);
      } else {
        throw new Error(response.data.message || 'Failed to get response');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        message: 'Sorry, I encountered an error while processing your request. Please try again.',
        timestamp: new Date().toISOString(),
        isUser: false
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    sendMessage(inputMessage);
  };

  const handleSuggestionClick = (suggestion: string) => {
    sendMessage(suggestion);
  };

  const formatMessage = (text: string) => {
    // Simple formatting for better readability
    return text.split('\n').map((line, index) => (
      <div key={index} className={index > 0 ? 'mt-2' : ''}>
        {line}
      </div>
    ));
  };

  return (
    <div className="flex flex-col h-full max-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-kpmgDarkBlue text-white p-4 flex items-center gap-3">
        <FaRobot className="text-2xl" />
        <div>
          <h2 className="text-xl font-bold">CMDB Assistant</h2>
          <p className="text-sm opacity-90">Ask questions about your infrastructure data</p>
        </div>
      </div>

      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-3xl rounded-lg p-4 ${
                message.isUser
                  ? 'bg-kpmgDarkBlue text-white ml-12'
                  : 'bg-white border border-gray-200 mr-12'
              }`}
            >
              <div className="flex items-start gap-3">
                <div className={`flex-shrink-0 ${message.isUser ? 'order-2' : ''}`}>
                  {message.isUser ? (
                    <FaUser className="text-lg" />
                  ) : (
                    <FaRobot className="text-lg text-kpmgDarkBlue" />
                  )}
                </div>
                <div className={`flex-1 ${message.isUser ? 'order-1' : ''}`}>
                  <div className={`text-sm ${message.isUser ? 'text-white' : 'text-gray-900'}`}>
                    {formatMessage(message.message)}
                  </div>
                  <div className={`text-xs mt-2 ${message.isUser ? 'text-blue-200' : 'text-gray-500'}`}>
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Loading indicator */}
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-white border border-gray-200 rounded-lg p-4 mr-12">
              <div className="flex items-center gap-3">
                <FaSpinner className="text-lg text-kpmgDarkBlue animate-spin" />
                <div className="text-gray-600">Thinking...</div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Suggestions */}
      {messages.length <= 1 && suggestions.length > 0 && (
        <div className="p-4 border-t border-gray-200 bg-white">
          <div className="flex items-center gap-2 mb-3">
            <FaLightbulb className="text-yellow-500" />
            <span className="text-sm font-medium text-gray-700">Suggested questions:</span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
            {suggestions.slice(0, 6).map((suggestion, index) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(suggestion)}
                className="text-left p-3 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm text-gray-700 transition-colors"
                disabled={isLoading}
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Input Form */}
      <div className="p-4 border-t border-gray-200 bg-white">
        <form onSubmit={handleSubmit} className="flex gap-3">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            placeholder="Ask me about your CMDB data..."
            className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-kpmgDarkBlue focus:border-transparent"
            disabled={isLoading}
          />
          <button
            type="submit"
            disabled={!inputMessage.trim() || isLoading}
            className="px-6 py-3 bg-kpmgDarkBlue text-white rounded-lg hover:bg-kpmgMediumBlue disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-colors"
          >
            {isLoading ? (
              <FaSpinner className="animate-spin" />
            ) : (
              <FaPaperPlane />
            )}
            Send
          </button>
        </form>
      </div>
    </div>
  );
};

export default CMDBChat;
