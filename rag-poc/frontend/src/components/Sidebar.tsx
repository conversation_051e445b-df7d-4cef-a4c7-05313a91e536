import React from "react";
import { Link, useLocation } from "react-router-dom";
import {
  FaHome,
  FaInfoCircle,
  FaDatabase,
} from "react-icons/fa";

const Sidebar = () => {
  const location = useLocation();

  const linkClass = (path: string) =>
    `flex items-center text-lg font-semibold px-2 py-1 rounded ${
      location.pathname === path
        ? "border-l-4 border-kpmgMediumBlue text-kpmgMediumBlue"
        : "hover:text-kpmgMediumBlue dark:hover:text-kpmgMediumBlue"
    }`;

  return (
    <div className="w-64 bg-backgroundLight dark:bg-backgroundDark text-primary dark:text-darkPrimary p-4 shadow-lg z-20 relative flex flex-col">
      <ul className="flex-grow">
        <li className="mb-4">
          <Link to="/" className={linkClass("/")}>
            <FaHome className="mr-3" />
            Home
          </Link>
        </li>
        <li className="mb-4">
          <Link to="/about" className={linkClass("/about")}>
            <FaInfoCircle className="mr-3" />
            About
          </Link>
        </li>
        <li className="mb-4">
          <Link to="/cmdb-dashboard" className={linkClass("/cmdb-dashboard")}>
            <FaDatabase className="mr-3" />
            CMDB Dashboard
          </Link>
        </li>
      </ul>

      <div className="mt-auto">
        <Link to="/about" className={linkClass("/about")}>
          <FaInfoCircle className="mr-3" />
          About
        </Link>
      </div>
    </div>
  );
};

export default Sidebar;
