import React from "react";
import { Link } from "react-router-dom";
import {
  FaArrowRight,
  FaDatabase,
  FaChartBar,
  FaComments,
} from "react-icons/fa"; // Importing icons

const Home = () => {
  return (
    <div className="container mx-auto p-4">
      <h2 className="text-2xl font-bold mb-4 text-primary dark:text-darkPrimary">
        Welcome to CMDB Management System
      </h2>
      <p className="text-primary dark:text-darkPrimary mb-6">
        This application provides comprehensive Configuration Management Database (CMDB) functionality
        with AI-powered analysis. Manage your infrastructure data, identify obsolete systems,
        and get intelligent insights about your IT environment.
      </p>

      {/* Navigation button */}
      <div className="mb-8">
        <Link
          to="/cmdb-dashboard"
          className="inline-flex items-center bg-kpmgDarkBlue text-white p-4 rounded-lg hover:bg-kpmgMediumBlue transition-colors text-lg"
        >
          <FaDatabase className="mr-3" />
          Open CMDB Dashboard
          <FaArrowRight className="ml-3" />
        </Link>
      </div>

      {/* CMDB Features */}
      <div className="mt-8">
        <h3 className="text-lg font-semibold text-primary dark:text-darkPrimary mb-4">
          CMDB Management Features
        </h3>
        <p className="text-primary dark:text-darkPrimary mb-4">
          Our CMDB system provides comprehensive infrastructure management capabilities
          powered by AI analysis. Explore your IT environment with these key features:
        </p>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="border-l-4 border-kpmgMediumBlue pl-4 bg-blue-50 p-4 rounded-r-lg">
            <div className="flex items-start">
              <FaDatabase className="text-kpmgMediumBlue mr-3 mt-1" size={20} />
              <div>
                <h4 className="font-semibold text-primary dark:text-darkPrimary mb-2">
                  Infrastructure Inventory
                </h4>
                <p className="text-primary dark:text-darkPrimary text-sm">
                  Complete database server inventory with detailed specifications,
                  application mappings, and business unit assignments.
                </p>
              </div>
            </div>
          </div>

          <div className="border-l-4 border-kpmgMediumBlue pl-4 bg-orange-50 p-4 rounded-r-lg">
            <div className="flex items-start">
              <FaChartBar className="text-kpmgMediumBlue mr-3 mt-1" size={20} />
              <div>
                <h4 className="font-semibold text-primary dark:text-darkPrimary mb-2">
                  Obsolescence Analysis
                </h4>
                <p className="text-primary dark:text-darkPrimary text-sm">
                  AI-powered analysis to identify outdated technology stacks,
                  unsupported software versions, and infrastructure at risk.
                </p>
              </div>
            </div>
          </div>

          <div className="border-l-4 border-kpmgMediumBlue pl-4 bg-green-50 p-4 rounded-r-lg">
            <div className="flex items-start">
              <FaComments className="text-kpmgMediumBlue mr-3 mt-1" size={20} />
              <div>
                <h4 className="font-semibold text-primary dark:text-darkPrimary mb-2">
                  AI Assistant
                </h4>
                <p className="text-primary dark:text-darkPrimary text-sm">
                  Natural language interface to query your CMDB data.
                  Ask questions and get intelligent insights about your infrastructure.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-primary dark:text-darkPrimary mb-2">
            Current Data Overview
          </h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-kpmgDarkBlue">100</div>
              <div className="text-sm text-gray-600">Database Servers</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-kpmgDarkBlue">56</div>
              <div className="text-sm text-gray-600">Applications</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-kpmgDarkBlue">5</div>
              <div className="text-sm text-gray-600">Database Types</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-kpmgDarkBlue">6</div>
              <div className="text-sm text-gray-600">Business Units</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
