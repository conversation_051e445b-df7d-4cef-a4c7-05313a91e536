import React, { useState } from 'react';
import { FaDatabase, FaComments, FaChartBar } from 'react-icons/fa';
import CMDBDataTable from '../components/CMDBDataTable';
import C<PERSON>BChat from '../components/CMDBChat';

type TabType = 'data' | 'chat' | 'analytics';

const CMDBDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('data');

  const tabs = [
    {
      id: 'data' as TabType,
      label: 'CMDB Data',
      icon: FaDatabase,
      description: 'View and manage configuration management database'
    },
    {
      id: 'chat' as TabType,
      label: 'AI Assistant',
      icon: FaComments,
      description: 'Chat with AI about your infrastructure data'
    },
    {
      id: 'analytics' as TabType,
      label: 'Analytics',
      icon: FaChartBar,
      description: 'Infrastructure insights and obsolescence analysis'
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'data':
        return <CMDBDataTable />;
      case 'chat':
        return (
          <div className="h-full">
            <CMDBChat />
          </div>
        );
      case 'analytics':
        return (
          <div className="container mx-auto p-4">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-xl font-bold text-kpmgDarkBlue mb-4">Infrastructure Analytics</h3>
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-800 mb-2">Server Distribution</h4>
                  <p className="text-sm text-blue-600">
                    Analyze server distribution across different database types and operating systems.
                  </p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-green-800 mb-2">Resource Utilization</h4>
                  <p className="text-sm text-green-600">
                    Monitor CPU, RAM, and storage utilization across your infrastructure.
                  </p>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-orange-800 mb-2">Obsolescence Risk</h4>
                  <p className="text-sm text-orange-600">
                    Identify servers and applications at risk of becoming obsolete.
                  </p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-purple-800 mb-2">Compliance Status</h4>
                  <p className="text-sm text-purple-600">
                    Track compliance with security and operational standards.
                  </p>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-red-800 mb-2">Security Alerts</h4>
                  <p className="text-sm text-red-600">
                    Monitor security vulnerabilities and patch requirements.
                  </p>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-800 mb-2">Cost Optimization</h4>
                  <p className="text-sm text-gray-600">
                    Identify opportunities for infrastructure cost optimization.
                  </p>
                </div>
              </div>
              
              <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 className="font-semibold text-yellow-800 mb-2">🚧 Coming Soon</h4>
                <p className="text-sm text-yellow-700">
                  Advanced analytics features including interactive charts, trend analysis, 
                  and predictive insights are currently in development.
                </p>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Page Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">CMDB Dashboard</h1>
            <p className="text-sm text-gray-600 mt-1">
              Configuration Management Database - Infrastructure Overview
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
              System Online
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-kpmgDarkBlue text-kpmgDarkBlue'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Description */}
      <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
        <p className="text-sm text-gray-600">
          {tabs.find(tab => tab.id === activeTab)?.description}
        </p>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default CMDBDashboard;
