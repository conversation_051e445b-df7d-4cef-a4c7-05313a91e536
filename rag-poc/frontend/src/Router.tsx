import React, { Suspense, lazy } from "react";
import { Routes, Route } from "react-router-dom";

const Home = lazy(() => import("./pages/Home"));
const About = lazy(() => import("./pages/About"));
const NoMatch = lazy(() => import("./components/NoMatch"));
const Wrapper = lazy(() => import("./components/Wrapper"));
const CMDBDashboard = lazy(() => import("./pages/CMDBDashboard"));

type ChildRoute = {
  path: string;
  Component: React.ElementType;
  is_index?: boolean;
};

type AppRoute = {
  path: string;
  Component: React.ElementType;
  childs?: ChildRoute[];
};

const routers: AppRoute[] = [
  { path: "/", Component: Home },
  { path: "/about", Component: About },
  { path: "/cmdb-dashboard", Component: CMDBDashboard },
  { path: "*", Component: NoMatch },
];

const Routers: React.FC = () => {
  return (
    <Routes>
      {routers.map(({ path, Component, childs }) => (
        <Route
          key={path}
          path={path}
          element={
            <Suspense fallback={<div>Loading...</div>}>
              <Wrapper>
                <Component />
              </Wrapper>
            </Suspense>
          }
        >
          {childs?.map((child, idx) => (
            <Route
              key={idx}
              path={child.is_index ? undefined : child.path}
              index={child.is_index || false}
              element={
                <Suspense fallback={<div>Loading...</div>}>
                  <child.Component />
                </Suspense>
              }
            />
          ))}
        </Route>
      ))}
    </Routes>
  );
};

export default Routers;
