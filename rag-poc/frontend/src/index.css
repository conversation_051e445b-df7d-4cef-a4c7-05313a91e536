/* index.css */

/* Tailwind Imports */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global Styles */
:root {
  --primary-color-light: #00338d; /* KPMG Blue - Light mode primary color */
  --secondary-color-light: #005eb8; /* Medium Blue - Light mode secondary color */
  --accent-color-light: #0091da; /* Light Blue - Accent color */
  --background-light: #f5f5f5; /* Light background */
}

body {
  background-color: var(--background-light);
  color: var(--primary-color-light);
}

a {
  color: var(--secondary-color-light);
}

/* Dark Mode Overrides */
.dark {
  --primary-color-dark: #00338d; /* KPMG Blue - Dark mode primary color */
  --secondary-color-dark: #005eb8; /* Medium Blue - Dark mode secondary color */
  --accent-color-dark: #0091da; /* Light Blue - Dark mode accent color */
  --background-dark: #1a1a1a; /* Dark background */

  background-color: var(--background-dark);
  color: var(--primary-color-dark);
}

.dark a {
  color: var(--secondary-color-dark);
}

html {
  transition: background-color 0.3s ease, color 0.3s ease;
}
